import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { schedule_log_status } from '@prisma/client';

interface TimeSlot {
  id: number;
  hourly_rate: number;
  start_time: string; // "HH:mm"
  end_time: string;   // "HH:mm"
  break_time?: number;
  name: string;
}

interface ShiftBreakdown {
  shift: TimeSlot;
  start: string;
  end: string;
  hours_worked: number;
  rate: number;
  amount: number;
  break_time: number;
}

interface ShiftBreakdownResult {
  total: number;
  total_duration_minutes: number;
  breakdown: ShiftBreakdown[];
}

@Injectable()
export class ScheduleLogsUtilsService {
  constructor(private prisma: PrismaService) { }

  async calculateDurationAndAmounts(schedule_log_id: number) {
    const log = await this.prisma.schedule_logs.findUnique({
      where: { id: schedule_log_id },
      include: {
        schedules: {
          select: {
            end_date: true,
          },
        },
        company: {
          select: {
            extra_pay_rate: true,
          },
        },
        employee: {
          select: {
            has_driving_license: true,
          },
        },
      },
    });

    if (!log?.clock_in_at || !log?.clock_out_at) {
      throw new NotFoundException('Schedule log not found or missing clock times.');
    }

    const timeSlotsRaw = await this.prisma.time_slot_rates.findMany({
      where: { company_id: log.company_id },
      orderBy: { start_time: 'asc' },
    });

    const timeSlots: TimeSlot[] = timeSlotsRaw.map(slot => ({
      id: slot.id,
      hourly_rate: slot.hourly_rate !== null ? Number(slot.hourly_rate) : 0,
      break_time: slot.break_time ?? 0,
      name: slot.name ?? '',
      start_time: this.formatTimeFromDate(new Date(slot.start_time)),
      end_time: this.formatTimeFromDate(new Date(slot.end_time)),
    }));

    const clockInAt = new Date(log.clock_in_at);
    const clockOutAt = new Date(log.clock_out_at);
    const scheduleEnd = log?.schedules?.end_date ? new Date(log.schedules.end_date) : null;

    // Debug logging to help troubleshoot
    console.log('Debug - Clock In:', clockInAt.toISOString());
    console.log('Debug - Clock Out:', clockOutAt.toISOString());
    console.log('Debug - Schedule End:', scheduleEnd?.toISOString());

    const isOvertime = scheduleEnd && clockOutAt > scheduleEnd;
    console.log('Debug - Is Overtime:', isOvertime);

    let breakTime = log.break_time;
    let status: schedule_log_status;

    let workResult: ShiftBreakdownResult = {
      total: 0,
      total_duration_minutes: 0,
      breakdown: [],
    };
    let overtimeResult: ShiftBreakdownResult = {
      total: 0,
      total_duration_minutes: 0,
      breakdown: [],
    };

    if (isOvertime && scheduleEnd) {
      console.log('Debug - Calculating overtime');
      
      // Calculate work period (clock in to schedule end)
      workResult = await this.calculateShiftBreakdown(
        clockInAt,
        scheduleEnd,
        timeSlots,
        log?.break_start ? new Date(log.break_start) : undefined,
        log.break_end ? new Date(log.break_end) : undefined
      );

      // Calculate overtime period (schedule end to clock out)
      overtimeResult = await this.calculateShiftBreakdown(
        scheduleEnd,
        clockOutAt,
        timeSlots,
        log?.break_start ? new Date(log.break_start) : undefined,
        log.break_end ? new Date(log.break_end) : undefined
      );

      console.log('Debug - Work Result:', workResult);
      console.log('Debug - Overtime Result:', overtimeResult);

      status = schedule_log_status.pending;
    } else {
      console.log('Debug - No overtime, calculating regular work');
      
      workResult = await this.calculateShiftBreakdown(
        clockInAt,
        clockOutAt,
        timeSlots,
        log?.break_start ? new Date(log.break_start) : undefined,
        log.break_end ? new Date(log.break_end) : undefined
      );

      status = schedule_log_status.approved;
    }

    let totalPay = +(workResult.total + overtimeResult.total);
    const totalDuration = workResult.total_duration_minutes + overtimeResult.total_duration_minutes;
    let extra_pay = 0;

    if (log.employee?.has_driving_license == true) {
      const extraPay = log.company?.extra_pay_rate;
      extra_pay = (Number(totalDuration) / 60) * Number(extraPay);
      totalPay = Number(totalPay) + extra_pay;
    }

    const result = this.prisma.schedule_logs.update({
      where: { id: schedule_log_id },
      data: {
        total_pay: totalPay,
        work_pay: workResult.total,
        overtime_pay: overtimeResult.total,
        work_duration: workResult.total_duration_minutes,
        overtime_duration: overtimeResult.total_duration_minutes,
        duration: totalDuration,
        break_time: breakTime,
        status,
        work_pay_split: JSON.parse(JSON.stringify(workResult.breakdown)),
        overtime_pay_split: JSON.parse(JSON.stringify(overtimeResult.breakdown)),
        extra_pay
      },
    });

    await this.createScheduleLogTimeSlots(schedule_log_id, workResult.breakdown, overtimeResult.breakdown);
    return result;
  }

  private formatTimeFromDate(date: Date): string {
    return date.toTimeString().slice(0, 5); // "HH:mm"
  }

  private parseTime(timeStr: string): { hours: number; minutes: number } {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return { hours, minutes };
  }

  private addMinutesToDate(date: Date, minutes: number): Date {
    return new Date(date.getTime() + minutes * 60000);
  }

  private getMinutesDifference(start: Date, end: Date): number {
    return Math.floor((end.getTime() - start.getTime()) / 60000);
  }

  async calculateShiftBreakdown(
    clockIn: Date,
    clockOut: Date,
    shifts: TimeSlot[],
    breakStart?: Date,
    breakEnd?: Date,
  ): Promise<ShiftBreakdownResult> {
    console.log('Debug - calculateShiftBreakdown called with:');
    console.log('  Clock In:', clockIn.toISOString());
    console.log('  Clock Out:', clockOut.toISOString());
    console.log('  Shifts count:', shifts.length);

    const breakdown: ShiftBreakdown[] = [];
    let totalMinutes = 0;

    // Ensure clockOut is after clockIn
    if (clockOut <= clockIn) {
      console.log('Debug - Clock out is not after clock in, returning empty result');
      return {
        total: 0,
        total_duration_minutes: 0,
        breakdown: [],
      };
    }

    // Get the date range we need to check - extend to cover overnight shifts
    const startDate = new Date(clockIn);
    const endDate = new Date(clockOut);

    // Check each day from clock in to clock out, including the day before for overnight shifts
    let currentDate = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate() - 1);
    const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());

    console.log('Debug - Date range (extended):', currentDate.toDateString(), 'to', endDateOnly.toDateString());

    while (currentDate <= endDateOnly) {
      console.log('Debug - Processing date:', currentDate.toDateString());
      
      for (const shift of shifts) {
        console.log('Debug - Processing shift:', shift.name, shift.start_time, '-', shift.end_time);
        
        const { shiftStart, shiftEnd } = this.getShiftTimes(currentDate, shift);
        console.log('  Shift times:', shiftStart.toISOString(), 'to', shiftEnd.toISOString());

        // Check if there's any overlap between the work period and this shift
        const hasOverlap = clockIn < shiftEnd && clockOut > shiftStart;
        
        if (!hasOverlap) {
          console.log('  No overlap, skipping');
          continue;
        }

        // Find actual overlap times
        const overlapStart = new Date(Math.max(clockIn.getTime(), shiftStart.getTime()));
        const overlapEnd = new Date(Math.min(clockOut.getTime(), shiftEnd.getTime()));

        console.log('  Overlap:', overlapStart.toISOString(), 'to', overlapEnd.toISOString());

        let workMinutes = this.getMinutesDifference(overlapStart, overlapEnd);
        let breakMinutes = 0;

        console.log('  Work minutes before break:', workMinutes);

        // Calculate break time that overlaps with this shift period
        if (breakStart && breakEnd) {
          const breakOverlapStart = new Date(Math.max(overlapStart.getTime(), breakStart.getTime()));
          const breakOverlapEnd = new Date(Math.min(overlapEnd.getTime(), breakEnd.getTime()));

          if (breakOverlapStart < breakOverlapEnd) {
            breakMinutes = this.getMinutesDifference(breakOverlapStart, breakOverlapEnd);
            workMinutes -= breakMinutes;
            console.log('  Break minutes:', breakMinutes, 'Work minutes after break:', workMinutes);
          }
        }

        if (workMinutes <= 0) {
          console.log('  No work minutes, skipping');
          continue;
        }

        const hoursWorked = workMinutes / 60;
        const amount = +(hoursWorked * shift.hourly_rate);

        console.log('  Final: hours =', hoursWorked, 'rate =', shift.hourly_rate, 'amount =', amount);

        breakdown.push({
          shift,
          start: overlapStart.toISOString(),
          end: overlapEnd.toISOString(),
          hours_worked: +(Math.round(hoursWorked * 100) / 100),
          rate: shift.hourly_rate,
          amount: +(Math.round(amount * 100) / 100),
          break_time: breakMinutes,
        });

        totalMinutes += workMinutes;
      }

      // Move to next day
      currentDate = new Date(currentDate.getTime() + 24 * 60 * 60 * 1000);
    }

    const total = breakdown.reduce((acc, item) => acc + item.amount, 0);

    console.log('Debug - Final breakdown result:', {
      total: +total,
      total_duration_minutes: totalMinutes,
      breakdown_count: breakdown.length
    });

    return {
      total: +(Math.round(total * 100) / 100),
      total_duration_minutes: totalMinutes,
      breakdown,
    };
  }

  private getShiftTimes(date: Date, shift: TimeSlot): { shiftStart: Date; shiftEnd: Date } {
    const startTime = this.parseTime(shift.start_time);
    const endTime = this.parseTime(shift.end_time);

    const shiftStart = new Date(date);
    shiftStart.setHours(startTime.hours, startTime.minutes, 0, 0);

    let shiftEnd = new Date(date);
    shiftEnd.setHours(endTime.hours, endTime.minutes, 0, 0);

    // Handle overnight shifts (end time is before start time)
    if (shift.end_time < shift.start_time) {
      shiftEnd = new Date(shiftEnd.getTime() + 24 * 60 * 60 * 1000); // Add one day
    }

    return { shiftStart, shiftEnd };
  }

  async createScheduleLogTimeSlots(
    schedule_log_id: number,
    workBreakdown: ShiftBreakdown[],
    overtimeBreakdown: ShiftBreakdown[]
  ) {
    await this.prisma.schedule_log_time_slots.deleteMany({
      where: {
        schedule_log_id
      }
    });
    
    const allBreakdowns = [...(workBreakdown || []), ...(overtimeBreakdown || [])];

    const toCreate = allBreakdowns
      .filter(b => b.amount > 0)
      .map(b => ({
        schedule_log_id,
        time_slot_rate_id: b.shift.id,
        break_time: b.break_time,
        hours_worked: b.hours_worked,
        start: b.start ? new Date(b.start) : undefined,
        end: b.end ? new Date(b.end) : undefined,
        rate: b.rate,
        amount: b.amount,
      }));

    if (toCreate.length === 0) return [];

    return this.prisma.schedule_log_time_slots.createMany({
      data: toCreate,
      skipDuplicates: true,
    });
  }
}