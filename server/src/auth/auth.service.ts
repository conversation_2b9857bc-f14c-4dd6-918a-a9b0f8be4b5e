import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createSupabaseClient } from '../config/supabase.config';
import { AuthError } from '@supabase/supabase-js';
import { SupabaseService } from '../supabase/supabase.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { Request } from 'express';
import { PrismaService } from '../prisma/prisma.service';
import { AuthResponseDto } from './dto/auth-response.dto';
import { Prisma } from '@prisma/client';

type UserRoleType = 'super_admin' | 'company_admin' | 'employee' | 'company_staff';

interface UserRole {
  role: UserRoleType;
  company_id: string | null;
  employee_id: string | null;
}

@Injectable()
export class AuthService {
  private supabase;

  constructor(
    private configService: ConfigService,
    private readonly supabaseService: SupabaseService,
    private readonly prisma: PrismaService,
  ) {
    this.supabase = createSupabaseClient(configService);
  }

  private getUserResponse(user: any, userRole: UserRole) {
    const baseUser = {
      id: user.id,
      email: user.email || '',
      name: user.user_metadata?.name || (user.email ? user.email.split('@')[0] : 'User'),
      role: userRole.role,
    };

    // Add the appropriate ID based on role
    switch (userRole.role) {
      case 'employee':
        return {
          ...baseUser,
          employee_id: userRole.employee_id?.toString() || undefined,
          company_id: userRole.company_id?.toString() || undefined,
        };
      case 'company_admin':
        return {
          ...baseUser,
          company_id: userRole.company_id?.toString() || undefined,
        };
      case 'company_staff':
        return {
          ...baseUser,
          company_id: userRole.company_id?.toString() || undefined,
        };
      case 'super_admin':
        return baseUser;
      default:
        return baseUser;
    }
  }

  async login(email: string, password: string): Promise<AuthResponseDto> {
    try {
      // Authenticate with Supabase
      const { data: authData, error: authError } = await this.supabaseService.client.auth.signInWithPassword({
        email,
        password,
      });

      if (authError) {
        console.error('Supabase auth error:', authError);
        throw new UnauthorizedException('Invalid credentials');
      }

      if (!authData.user) {
        throw new UnauthorizedException('User not found');
      }

      // Get user role from database
      const userRole = await this.getUserRole(authData.user.id);

      // Format user data, always string or undefined for company_id/employee_id
      const user = {
        id: authData.user.id,
        email: authData.user.email,
        name: authData.user.user_metadata?.name || '',
        role: userRole.role,
        company_id: userRole.company_id ?? undefined,
        employee_id: userRole.employee_id ?? undefined,
      };

      return {
        access_token: authData.session?.access_token || '',
        refresh_token: authData.session?.refresh_token || '',
        user,
      };
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  private async getUserRole(userId: string): Promise<UserRole> {
    try {
      const result = await this.prisma.$queryRaw<Array<UserRole>>(
        Prisma.sql`SELECT role, company_id, employee_id FROM user_roles WHERE user_id = ${userId}::uuid`
      );

      if (!result || result.length === 0) {
        throw new UnauthorizedException('User role not found');
      }

      return result[0];
    } catch (error) {
      console.error('Error getting user role:', error);
      throw error;
    }
  }

  async register(email: string, password: string, name: string, role: UserRoleType, companyId?: string, employeeId?: string): Promise<AuthResponseDto> {
    try {
      // Register with Supabase
      const { data: authData, error: authError } = await this.supabaseService.client.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
        },
      });

      if (authError) {
        console.error('Supabase registration error:', authError);
        throw new UnauthorizedException('Registration failed');
      }

      if (!authData.user) {
        throw new UnauthorizedException('User creation failed');
      }

      // Create user role in database
      const userRole = await this.createUserRole(authData.user.id, role, companyId, employeeId);

      // Format user data, always string or undefined for company_id/employee_id
      const user = {
        id: authData.user.id,
        email: authData.user.email,
        name: authData.user.user_metadata?.name || '',
        role: userRole.role,
        company_id: userRole.company_id ?? undefined,
        employee_id: userRole.employee_id ?? undefined,
      };

      return {
        access_token: authData.session?.access_token || '',
        refresh_token: authData.session?.refresh_token || '',
        user,
      };
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  private async createUserRole(userId: string, role: UserRoleType, companyId?: string, employeeId?: string): Promise<UserRole> {
    try {
      const result = await this.prisma.$queryRaw<Array<UserRole>>(
        Prisma.sql`INSERT INTO user_roles (user_id, role, company_id, employee_id) VALUES (${userId}, ${role}, ${companyId}, ${employeeId}) RETURNING role, company_id, employee_id`
      );

      if (!result || result.length === 0) {
        throw new UnauthorizedException('Failed to create user role');
      }

      return result[0];
    } catch (error) {
      console.error('Error creating user role:', error);
      throw error;
    }
  }

  async logout(): Promise<{ message: string }> {
    const { error } = await this.supabaseService.client.auth.signOut();
    if (error) {
      throw new UnauthorizedException('Logout failed');
    }
    return { message: 'Logged out successfully' };
  }

  async getCurrentUser(req: Request) {
    const token = req.headers.authorization?.split(' ')[1];
    if (!token) {
      throw new UnauthorizedException('No token provided');
    }

    const { data: { user }, error } = await this.supabaseService.client.auth.getUser(token);

    if (error || !user) {
      throw new UnauthorizedException('Invalid or expired token');
    }

    // Get user role and employee info with retry logic
    const userRoleArr = await this.prisma.$queryRawWithRetry<UserRole[]>(
      Prisma.sql`SELECT role, company_id, employee_id FROM user_roles WHERE user_id = ${user.id}::uuid`
    );
    const userRole = userRoleArr && userRoleArr[0];

    if (!userRole) {
      throw new UnauthorizedException('User role not found');
    }

    return this.getUserResponse(user, userRole);
  }

  async validateUser(userId: string): Promise<AuthResponseDto> {
    try {
      // Get user from Supabase
      const { data: { user }, error: userError } = await this.supabaseService.client.auth.getUser();

      if (userError || !user) {
        throw new UnauthorizedException('Invalid user');
      }

      // Get user role from database
      const userRole = await this.getUserRole(userId);

      // Format user data, always string or undefined for company_id/employee_id
      const userData = {
        id: user.id,
        email: user.email,
        name: user.user_metadata?.name || '',
        role: userRole.role,
        company_id: userRole.company_id ?? undefined,
        employee_id: userRole.employee_id ?? undefined,
      };

      return {
        access_token: '', // No new token needed for validation
        refresh_token: '', // No new token needed for validation
        user: userData,
      };
    } catch (error) {
      console.error('User validation error:', error);
      throw error;
    }
  }

  async refreshToken(refreshToken: string): Promise<AuthResponseDto> {
    try {
      // Refresh session with Supabase
      const { data: authData, error: authError } = await this.supabaseService.client.auth.refreshSession({
        refresh_token: refreshToken,
      });

      if (authError) {
        console.error('Supabase refresh token error:', authError);
        throw new UnauthorizedException('Invalid or expired refresh token');
      }

      if (!authData.user || !authData.session) {
        throw new UnauthorizedException('Failed to refresh token');
      }

      // Get user role from database
      const userRole = await this.getUserRole(authData.user.id);

      // Format user data, always string or undefined for company_id/employee_id
      const user = {
        id: authData.user.id,
        email: authData.user.email,
        name: authData.user.user_metadata?.name || '',
        role: userRole.role,
        company_id: userRole.company_id ?? undefined,
        employee_id: userRole.employee_id ?? undefined,
      };

      return {
        access_token: authData.session.access_token || '',
        refresh_token: authData.session.refresh_token || '',
        user,
      };
    } catch (error) {
      console.error('Refresh token error:', error);
      throw error;
    }
  }
} 