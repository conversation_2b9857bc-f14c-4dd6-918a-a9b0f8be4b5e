"use client";

import { useAuth } from "@/components/auth/auth-provider";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { hasRole, Role } from "@/lib/roles";

interface RoleGuardProps {
  allowedRoles: Role[];
  children: React.ReactNode;
  redirectTo?: string;
}

export function RoleGuard({
  allowedRoles,
  children,
  redirectTo = "/employee",
}: RoleGuardProps) {
  const { user } = useAuth();
  const router = useRouter();

  const userHasAccess = hasRole(user, allowedRoles);

  useEffect(() => {
    if (user && !userHasAccess) {
      // User doesn't have required role, redirect them
      router.push(redirectTo);
    }
  }, [user, userHasAccess, redirectTo, router]);

  // Don't render content if user doesn't have required role
  if (!user || !userHasAccess) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">
            Access Denied
          </h2>
          <p className="text-gray-600">
            You don't have permission to access this page.
          </p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
