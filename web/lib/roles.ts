import { User } from "@/types/auth";

export const ROLES = {
  SUPER_ADMIN: "super_admin",
  COMPANY_ADMIN: "company_admin",
  COMPANY_STAFF: "company_staff",
} as const;

export type Role = (typeof ROLES)[keyof typeof ROLES];

/**
 * Check if user has any of the specified roles
 */
export function hasRole(user: User | null, roles: Role[]): boolean {
  if (!user || !user.role) return false;
  return roles.includes(user.role as Role);
}

/**
 * Check if user is a company admin
 */
export function isCompanyAdmin(user: User | null): boolean {
  return hasRole(user, [ROLES.COMPANY_ADMIN]);
}

/**
 * Check if user is company staff
 */
export function isCompanyStaff(user: User | null): boolean {
  return hasRole(user, [ROLES.COMPANY_STAFF]);
}

/**
 * Check if user can access timesheet features
 */
export function canAccessTimesheet(user: User | null): boolean {
  return hasRole(user, [ROLES.COMPANY_ADMIN]);
}

/**
 * Check if user can access payroll settings
 */
export function canAccessPayrollSettings(user: User | null): boolean {
  return hasRole(user, [ROLES.COMPANY_ADMIN]);
}

/**
 * Check if user can access employee management
 */
export function canAccessEmployeeManagement(user: User | null): boolean {
  return hasRole(user, [ROLES.COMPANY_ADMIN, ROLES.COMPANY_STAFF]);
}

/**
 * Check if user can access logs
 */
export function canAccessLogs(user: User | null): boolean {
  return hasRole(user, [ROLES.COMPANY_ADMIN, ROLES.COMPANY_STAFF]);
}

/**
 * Check if user can access schedule features
 */
export function canAccessSchedule(user: User | null): boolean {
  return hasRole(user, [ROLES.COMPANY_ADMIN, ROLES.COMPANY_STAFF]);
}

/**
 * Get allowed sidebar items based on user role
 */
export function getAllowedSidebarItems(user: User | null) {
  const items = [];

  if (canAccessEmployeeManagement(user)) {
    items.push("employee");
  }

  if (canAccessLogs(user)) {
    items.push("logs");
  }

  if (canAccessTimesheet(user)) {
    items.push("timesheet");
  }

  if (canAccessSchedule(user)) {
    items.push("schedule");
  }

  if (canAccessPayrollSettings(user)) {
    items.push("settings");
  }

  return items;
}
